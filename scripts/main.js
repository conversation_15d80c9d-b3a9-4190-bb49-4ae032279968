// Header hide/reveal + shadow
(() => {
  const header = document.getElementById("site-header");
  if (!header) return;
  let lastY = window.scrollY;
  let ticking = false;
  const onScroll = () => {
    const y = window.scrollY;
    header.classList.toggle("shadow", y > 4);
    if (y > lastY && y > 120) header.style.transform = "translateY(-100%)";
    else header.style.transform = "translateY(0)";
    lastY = y; ticking = false;
  };
  window.addEventListener("scroll", () => {
    if (!ticking) { window.requestAnimationFrame(onScroll); ticking = true; }
  }, { passive: true });
})();

// IntersectionObserver for reveal animations
(() => {
  const io = new IntersectionObserver((entries) => {
    entries.forEach((e) => { if (e.isIntersecting) e.target.classList.add("in-view"); });
  }, { rootMargin: "0px 0px -10% 0px", threshold: 0.15 });
  document.querySelectorAll(".inview\\:animate-fade-up").forEach(el => io.observe(el));
})();

// Mobile menu accordion
(() => {
  const btn = document.getElementById("mobileMenuBtn");
  const menu = document.getElementById("mobileMenu");
  if (!btn || !menu) return;
  btn.addEventListener("click", () => {
    const open = btn.getAttribute("aria-expanded") === "true";
    btn.setAttribute("aria-expanded", String(!open));
    menu.style.maxHeight = open ? "0px" : menu.scrollHeight + "px";
  });
})();

// Region dialog
(() => {
  const dialog = document.getElementById("regionDialog");
  const triggers = [document.getElementById("regionBtn"), document.getElementById("regionBtnFooter")].filter(Boolean);
  if (!dialog) return;
  triggers.forEach(btn => btn.addEventListener("click", () => {
    dialog.showModal();
    dialog.querySelector("button, input")?.focus();
  }));
  dialog.addEventListener("click", (e) => {
    const rect = dialog.getBoundingClientRect();
    const inside = e.clientX >= rect.left && e.clientX <= rect.right && e.clientY >= rect.top && e.clientY <= rect.bottom;
    if (!inside) dialog.close();
  });
})();

// Marquee duplication for seamlessness
(() => {
  const m = document.querySelector(".marquee");
  if (!m || m.dataset.cloned) return;
  const clone = m.cloneNode(true);
  m.dataset.cloned = "true";
  m.parentNode.style.position = "relative";
  m.parentNode.appendChild(clone);
  clone.style.position = "absolute";
  clone.style.left = "100%";
})();
