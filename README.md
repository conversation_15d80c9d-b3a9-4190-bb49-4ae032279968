# Nothing India Homepage Clone

A pixel-perfect educational clone of the Nothing India homepage built with Tailwind CSS and vanilla JavaScript.

## Features

- ✨ Pixel-perfect design matching Nothing's aesthetic
- 📱 Fully responsive (mobile-first approach)
- 🎨 Custom Nothing brand fonts (NType82, Ndot, LetteraMono)
- 🌊 Smooth animations and interactions
- ♿ Accessibility-focused with ARIA attributes
- 🚀 Performance optimized with proper loading attributes
- 🎭 Interactive header, mobile menu, and region dialog

## Technologies

- **Tailwind CSS v4** - Utility-first CSS framework
- **Vanilla JavaScript** - Modern ES6+ with IntersectionObserver API
- **Nothing Brand Assets** - Authentic fonts and images scraped from the official site

## Project Structure

```
nothing-clone/
├── index.html              # Main HTML file
├── dist/
│   └── tailwind.css        # Compiled CSS
├── styles/
│   └── tailwind.css        # Source CSS with @import
├── scripts/
│   └── main.js             # Interactive functionality
├── assets/
│   ├── fonts/              # Nothing brand fonts
│   └── images/             # Product images and assets
├── tailwind.config.js      # Tailwind configuration
└── package.json            # Project configuration
```

## Getting Started

1. **Clone and navigate:**
   ```bash
   cd "nothing clone"
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Build CSS:**
   ```bash
   npm run build
   ```

4. **Start development server:**
   ```bash
   npm start
   ```

5. **Open in browser:**
   Navigate to `http://localhost:8000`

## Development

- **Watch mode:** `npm run dev` - Automatically rebuilds CSS on changes
- **Build only:** `npm run build` - Compile CSS for production
- **Serve only:** `npm run serve` - Start HTTP server

## Design System

### Custom Breakpoints
- `xs`: 360px - Small phones
- `sm`: 640px - Large phones  
- `md`: 768px - Tablets
- `lg`: 1024px - Laptops
- `xl`: 1280px - Desktops
- `2xl`: 1440px - Large desktops

### Nothing Brand Fonts
- **NType82**: Primary text (Regular/Headline)
- **Ndot**: Display and accent text
- **LetteraMono**: Monospace for labels

### Color Palette
- Neutral grays from `neutral-50` to `neutral-900`
- High contrast black/white theme
- Subtle opacity variations for depth

## Educational Purpose

This is a learning project created for educational purposes to demonstrate:
- Modern CSS techniques with Tailwind
- Responsive design patterns
- JavaScript DOM manipulation
- Performance optimization
- Accessibility best practices

## Credits

- Original design by Nothing Technology Limited
- Fonts and images are property of Nothing
- Educational implementation for learning purposes only

---

**Note:** This is an educational clone and not affiliated with Nothing Technology Limited.
